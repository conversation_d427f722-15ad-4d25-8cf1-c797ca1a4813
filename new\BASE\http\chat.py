from litellm import acompletion
import json
from logger.log import logger, LogDB, trace_context, set_trace_id, clear_trace_id
import time
from ..utils.utils import log_memory_usage
import os
from prompts import PROMPTS
from ..tools.tools_list import get as get_tools_list


MESSAGE_DELIMITER = "<__!!__END__!!__>"

@logger.catch()
def process_file_context(file_path: str) -> dict:
    """Read and process a file's content.
    
    Args:
        file_path: Path to the file
        context: Dictionary to store file content
    
    Returns:
        Updated context dictionary
    """
    if not file_path:
        logger.warning("No file path provided")
    
    try:
        # Read file content
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
            
        
        
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        content = ""
        
    return content

def truncate_file_content(content: str, max_tokens: int = 15000) -> str:
    """Truncate file content if it exceeds token limit
    
    Args:
        content: File content
        max_tokens: Maximum token limit
        
    Returns:
        Potentially truncated content
    """
    estimated_tokens = estimate_token_count(content)
    
    if estimated_tokens <= max_tokens:
        return content
    
    # Calculate approximate character limit
    char_limit = int((max_tokens / estimated_tokens) * len(content) * 0.9)  # 90% safety margin

    if char_limit < len(content):
        truncated = content[:char_limit]
        truncated += "\n\n[Content truncated due to size limit]"
        logger.warning(f"File content truncated from {len(content)} to {len(truncated)} characters")
        return truncated
    
    return content

def estimate_token_count(text: str) -> int:
            """Rough estimation of token count based on word count"""
            return len(text.split()) * 1.3  # Rough approximation

def process_context(ctx: dict) -> tuple[str, str]:
            """Process individual context and return replacement and additional content"""
            replacement_content = ""
            additional_content = ""
            
            if ctx["type"] == "file":
                content = process_file_context(ctx["path"])
                estimated_tokens = estimate_token_count(f"File Name: {ctx['name']}\nFile Path: {ctx['path']}\nFile Content:\n{content}\n\n")
                
                if estimated_tokens > 15000:
                    logger.warning(f"File {ctx['name']} content too large, may be truncated")
                    content = truncate_file_content(content)
                
                replacement_content = f"File: {ctx['path']}"
                additional_content = (
                    f"========\n"
                    f"File Name: {ctx['name']}\n"
                    f"File Path: {ctx['path']}\n"
                    f"File Content:\n{content}\n"
                    f"========\n"
                )
            elif ctx["type"] == "swagger":
                from .utils import process_swagger_context
                replacement_content, additional_content = process_swagger_context(ctx)

            elif ctx["type"] in ["terminal", "warnings", "errors", "commit"]:
                type_mapping = {
                    "terminal": "Terminal logs",
                    "warnings": "Warnings",
                    "errors": "Errors",
                    "commit": f"Commit {ctx.get('name', '')}"
                }
                replacement_content = type_mapping[ctx["type"]]
                additional_content = f"\n=====\n{type_mapping[ctx['type']]}: {ctx['content']}\n=====\n"
            
            return replacement_content, additional_content
    


async def chat_stream(llm_: dict, messages: list, call_for: str = "chat"):
    """
    Handle chat streaming requests.

    llm_ :: Dictionary containing LLM configuration. => {
        "base_url": str,  # Base URL for the LLM API
        "api_key": str,  # API key for authentication | Default => session_id for CodeMate Cloud Calls.
        "model": str,  # Model name to use
    }
    messages :: List of messages in the chat.
    messages format :: {
        "role": str, # system, user, assistant, tool_call
        "content": str | dict # Content of the message, can be a string or a dict containing string and image base64,
        "context": list | None # Optional, list of contexts to use for the message
    }
    tools :: List of tools available for the LLM to use.
    """

    has_yielded = False

    try:
        logger.info(f"[stream] Starting chat stream for {llm_.get('model')} with {len(messages)} messages")

        history_messages = messages

        SYSTEM_PROMPT = await PROMPTS.get(call_for)
        sequence_messages = [{
            "role": "system",
            "content": SYSTEM_PROMPT
        }]
        sequence_messages.extend(messages)
        # prepare tools
        # Extract contexts from user messages and get tools

        contexts = []
        for message in sequence_messages:
            if message["role"] == "user" and message.get("context"):
                context = message["context"]
                if not isinstance(context, list):
                    context = [context]
                    
                for ctx in context:
                    replacement, additional = process_context(ctx)
                    ctx["replacement_content"] = replacement
                    ctx["additional_content"] = additional
                    contexts.append(ctx)
                
                del message["context"]

        
        
        tools = get_tools_list(contexts)

        iteration = 0
        logger.info(f"[stream] Starting chat stream for {llm_.get('model')}")

        while True:
            iteration += 1
            # For custom endpoints, we need to prefix the model with openai/ to tell litellm to use OpenAI format
            model_name = llm_.get("model")
            if not model_name.startswith(("openai/", "anthropic/", "cohere/", "huggingface/")):
                model_name = f"openai/{model_name}"

            response = await acompletion(
                base_url=llm_.get("base_url", "https://llm.codemate.ai/v1"),
                api_key=llm_.get("api_key", ""),
                model=model_name,
                messages=sequence_messages,
                tools=tools if len(tools) != 0 else None,
                tool_choice="auto" if len(tools) != 0 else None,
                temperature=0.4,
                stream=True
            )


            logger.info(f"messages send to llm {json.dumps(sequence_messages, indent=2)}")
            chunks = []
            tool_calls = {}
            current_content = ""
            index = 0

            async for chunk in response:
                try:
                    # Safely access chunk data
                    if hasattr(chunk, 'choices') and len(chunk.choices) > 0:
                        choice = chunk.choices[0]
                        delta = choice.delta if hasattr(choice, 'delta') else None

                        if delta and hasattr(delta, 'tool_calls') and delta.tool_calls:
                            for tool_call in delta.tool_calls:
                                tool_call_index = tool_call.index
                                if tool_call_index not in tool_calls:
                                    tool_calls[tool_call_index] = {"name": "", "args": "", "id": ""}

                                if hasattr(tool_call, 'id') and tool_call.id:
                                    tool_calls[tool_call_index]["id"] = tool_call.id

                                if hasattr(tool_call, 'function') and tool_call.function:
                                    if hasattr(tool_call.function, 'name') and tool_call.function.name:
                                        tool_calls[tool_call_index]["name"] += tool_call.function.name

                                    if hasattr(tool_call.function, 'arguments') and tool_call.function.arguments:
                                        tool_calls[tool_call_index]["args"] += tool_call.function.arguments

                        if delta and hasattr(delta, 'content') and delta.content:
                            current_content += delta.content
                            has_yielded = True
                            yield json.dumps({
                                "index": index,
                                "type": "message",
                                "message": delta.content
                            }) + MESSAGE_DELIMITER

                except Exception as chunk_error:
                    logger.warning(f"Error processing chunk {index}: {str(chunk_error)}")
                    continue
                finally:
                    index += 1

            if current_content.strip() and not tool_calls:
                logger.info(f"[stream] Conversation completed after {iteration} iterations")
                break

            if tool_calls:
                logger.info(f"[stream] Processing {len(tool_calls)} tool calls")

                for tool_call_index, func_data in tool_calls.items():
                    if func_data["name"] and func_data["args"]:
                        has_yielded = True
                        yield json.dumps({
                            "index": tool_call_index,
                            "type": "tool_call",
                            "tool_call": {
                                "name": func_data["name"],
                                "arguments": func_data["args"]
                            }
                        }) + MESSAGE_DELIMITER

                # Break after processing tool calls to avoid infinite loop
                break

            # If no content and no tool calls, break to avoid infinite loop
            if not current_content.strip() and not tool_calls:
                logger.info(f"[stream] No content or tool calls, ending conversation")
                break

        # Ensure we always yield something
        if not has_yielded:
            yield json.dumps({
                "index": 0,
                "type": "message",
                "message": "No response generated"
            }) + MESSAGE_DELIMITER

        logger.debug(message=f"Chat stream completed for {llm_.get('model')}", extra={
            "_log_": "chat_stream",
            "messages": messages,
            "llm_": llm_,
            "chunks_returned": chunks
        })

    except Exception as e:
        logger.error(f"Error in chat_stream: {str(e)}")
        yield json.dumps({
            "index": 0,
            "type": "error",
            "message": f"Error: {str(e)}"
        }) + MESSAGE_DELIMITER