import os
import json
from typing import Any
from BASE.embeddings.embeddings import generate_embeddings_cloud


# -----------------------------------------------------------------------------
# Perform context search
# -----------------------------------------------------------------------------


async def _perform_remote_search(query: str, kbid: str) -> list[dict[str, Any]]:
    """
    Perform a remote vector search using cloud embeddings.
    """
    try:
        # Generate embeddings using cloud service
        embeddings = await generate_embeddings_cloud(False, query)

        # Simulate search results structure
        results = [
            {
                "file": f"search_result_{i}.py",
                "content": {"text": f"Search result {i} for query: {query}"},
                "additional_metadata": {
                    "line_start": i * 10,
                    "line_end": (i * 10) + 5,
                },
            }
            for i in range(3)
        ]
        return results
    except Exception as e:
        print(f"Error in perform_remote_search: {e}")
        return []


async def _perform_local_search(
    query: str,
    kbid: str,
    limit: int = 30,
) -> list[dict[str, Any]]:
    """
    Perform a local vector search using cloud embeddings.
    """
    try:
        # Generate embeddings using cloud service
        await generate_embeddings_cloud(False, query)

        # Simulate local search results using kbid
        results = [
            {
                "file": f"{kbid}_result_{i}.py",
                "content": {"text": f"Local search result {i} for query: {query}"},
                "additional_metadata": {
                    "line_start": i * 15,
                    "line_end": (i * 15) + 8,
                },
            }
            for i in range(min(limit, 5))
        ]
        return results
    except Exception as e:
        print(f"Error in perform_local_search: {e}")
        return []


# -----------------------------------------------------------------------------
# Process context search
# -----------------------------------------------------------------------------


async def process_context_search(
    query: str, tool_id: str, kbid: str, search_references
) -> tuple[list[dict[str, Any]], Any]:
    """Handle context search actions"""
    try:
        search_results = await _perform_local_search(
            query=query, kbid=kbid
        )

        for chunk in search_results:
            text = chunk["content"]["text"]
            filename = os.path.basename(chunk["file"])
            filepath = chunk["file"]
            search_references.add_search_result(
                path=filepath, name=filename, content=text, type="file"
            )

        new_messages = [
            {"role": "tool", "name": tool_id, "content": json.dumps(search_results)}
        ]

        return new_messages, search_references
    except Exception as e:
        print(f"Error in process_context_search: {e}")
        raise


# -----------------------------------------------------------------------------
