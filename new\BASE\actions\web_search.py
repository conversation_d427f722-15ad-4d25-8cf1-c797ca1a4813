import json
from BASE.embeddings.embeddings import generate_embeddings_cloud


# -----------------------------------------------------------------------------
# Perform web search
# -----------------------------------------------------------------------------


async def process_web_search(
    query: str, tool_id: str, session: str, search_references
):
    """
    Process a web search request using cloud embeddings.
    """
    try:
        # Generate embeddings using cloud service
        await generate_embeddings_cloud(False, query)

        # Simulate web search results using session context
        sources = [
            {
                "url": f"https://example.com/result_{i}",
                "title": f"Web result {i} for query: {query} (session: {session[:8]})",
                "content": f"Web content {i} related to {query}"
            }
            for i in range(3)
        ]

        for source in sources:
            search_references.add_search_result(
                path=source["url"],
                name=source["title"],
                content=source["content"],
                type="web"
            )

        message = [
            {"role": "tool", "name": tool_id, "content": json.dumps(sources)}
        ]

        return message, search_references
    except Exception as e:
        print(f"Error in process_web_search: {e}")
        raise
