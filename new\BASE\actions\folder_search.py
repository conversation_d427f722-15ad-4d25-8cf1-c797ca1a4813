import os
import json
from typing import Any
from BASE.embeddings.embeddings import generate_embeddings_cloud


# -----------------------------------------------------------------------------
# Perform folder search
# -----------------------------------------------------------------------------


async def _perform_folder_search(
    query: str,
    index_name: str,
    folder_path: str,
    limit: int = 10,
) -> list[dict[str, Any]]:
    """
    Perform a search within a specific folder using cloud embeddings.
    """
    try:
        # Generate embeddings using cloud service
        await generate_embeddings_cloud(False, query)

        # Simulate folder search results using index_name
        results = [
            {
                "file": f"{folder_path}/file_{i}.py",
                "content": {"text": f"Folder search result {i} for query: {query} in {folder_path} (index: {index_name})"},
                "additional_metadata": {
                    "line_start": i * 10,
                    "line_end": (i * 10) + 5,
                },
            }
            for i in range(min(limit, 3))
        ]
        return results
    except Exception as e:
        print(f"Error in perform_folder_search: {e}")
        return []


# -----------------------------------------------------------------------------
# Process folder search
# -----------------------------------------------------------------------------


async def process_folder_search(
    query: str,
    tool_id: str,
    folder_path: str,
    index_name: str,
    search_references,
):
    """Handle folder search actions"""
    try:
        search_results = await _perform_folder_search(
            query=query,
            index_name=index_name,
            folder_path=folder_path,
        )

        for chunk in search_results:
            text = chunk["content"]["text"]
            filename = os.path.basename(chunk["file"])
            filepath = chunk["file"]
            search_references.add_search_result(
                path=filepath, name=filename, content=text, type="file"
            )

        new_messages = [
            {"role": "tool", "name": tool_id, "content": json.dumps(search_results)}
        ]

        return new_messages, search_references
    except Exception as e:
        print(f"Error in process_folder_search: {e}")
        raise


# -----------------------------------------------------------------------------
