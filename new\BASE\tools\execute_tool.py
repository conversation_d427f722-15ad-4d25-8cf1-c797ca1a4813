


async def execute_tool_call(self, tool_call: dict[str, Any]) -> tuple[dict[str, Any], bool]:
        """
        Execute a tool call and return the result.

        Args:
            tool_call: Tool call from LLM response

        Returns:
            Tuple of (tool_result, needs_followup)
        """
        function_name = tool_call["function"]["name"]
        function_args = json.loads(tool_call["function"]["arguments"])
        tool_id = tool_call["id"]

        LOGGER.info(f"Executing tool call: {function_name} with args: {function_args}")

        try:
            # Emit tool call execution event (new granular event)
            # if hasattr(self.event_emitter, 'emit_tool_call_execution'):
                # await self.event_emitter.emit_tool_call_execution(tool_id, function_name)

            # Emit searching event before tool execution (backward compatibility)
            await self.event_emitter.emit(
                "chat_response_searching",
                data={"request_id": self.request_id, "action_id": tool_id}
            )

            # Route to appropriate action handler
            if function_name == "context_search":
                result, search_refs = await process_context_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    kbid=function_args["kbid"],
                    search_references=self.search_references
                )

            elif function_name == "folder_search":
                result, search_refs = await process_folder_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    folder_path=function_args["folder_path"],
                    index_name=function_args["kbid"],
                    search_references=self.search_references
                )

            elif function_name == "web_search":
                result, search_refs = await process_web_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    session=self.session_id,
                    search_references=self.search_references
                )

            elif function_name == "swagger_search":
                result, search_refs = await process_swagger_search(
                    query=function_args["query"],
                    tool_id=tool_id,
                    kbid=function_args["kbid"],
                    search_refrences=self.search_references
                )

            else:
                raise ValueError(f"Unknown tool function: {function_name}")

            # Update search references
            self.search_references = search_refs

            # Emit search references if available
            if search_refs and search_refs.get_search_result().get("results"):
                await self.event_emitter.emit(
                    "chat_response_references",
                    data=search_refs.get_search_result()
                )

            # Format tool result for LLM
            tool_result = {
                "tool_call_id": tool_id,
                "content": json.dumps(result)
            }

            # Emit tool call result event (new granular event)
            # if hasattr(self.event_emitter, 'emit_tool_call_result'):
            #     result_summary = f"Successfully executed {function_name}"
            #     if search_refs and search_refs.get_search_result().get("results"):
            #         result_count = len(search_refs.get_search_result().get("results", []))
            #         result_summary += f" - Found {result_count} results"

            #     await self.event_emitter.emit_tool_call_result(
            #         tool_id, function_name, True, result_summary
            #     )

            # Determine if LLM follow-up is needed
            needs_followup = self._needs_llm_followup(function_name, function_args, result)

            return tool_result, needs_followup

        except Exception as e:
            LOGGER.error(f"Error executing tool call {function_name}: {e}")

            # Emit tool call error event (new granular event)
            # if hasattr(self.event_emitter, 'emit_tool_call_error'):
            #     await self.event_emitter.emit_tool_call_error(tool_id, function_name, str(e))

            error_result = {
                "tool_call_id": tool_id,
                "content": json.dumps({
                    "error": str(e),
                    "status": "error"
                })
            }
            return error_result, True  # Error requires follow-up
