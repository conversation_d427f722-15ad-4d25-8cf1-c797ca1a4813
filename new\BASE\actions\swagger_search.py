import json
from typing import Any
from BASE.embeddings.embeddings import generate_embeddings_cloud


def query_api(query: str, results: list[dict]) -> list[str]:
    """Process API query results and return relevant paths."""
    results_xml = []
    for result in results:
        endpoint_str = "<endpoint>\n"
        endpoint_str += f"  <path>{result['name']}</path>\n"
        endpoint_str += f"  <summary>{result['content']}</summary>\n"
        endpoint_str += "</endpoint>"
        results_xml.append(endpoint_str)

    # Simulate API path selection
    paths = [f"/api/path_{i}" for i in range(min(len(results), 3))]
    return paths


async def _perform_swagger_search(
    query: str,
    index_name: str,
    limit: int = 20,
) -> list[dict[str, Any]]:
    """Perform swagger search using cloud embeddings."""
    try:
        # Generate embeddings using cloud service
        await generate_embeddings_cloud(False, query)

        # Simulate swagger search results using index_name
        mock_results = [
            {
                "name": f"/api/{index_name}/endpoint_{i}",
                "content": f"Swagger endpoint {i} for query: {query}",
                "additional_metadata": {
                    "method": "GET" if i % 2 == 0 else "POST",
                    "description": f"API endpoint {i} description"
                }
            }
            for i in range(min(limit, 5))
        ]

        # Process with query_api
        api_results = query_api(query=query, results=mock_results)

        # Create results map
        results_map = {result["name"]: result for result in mock_results}
        filtered_results = [results_map.get(path, {}) for path in api_results if path in results_map]

        return filtered_results
    except Exception as e:
        print(f"Error in performing swagger_search: {e}")
        return []


async def process_swagger_search(
    query: str, tool_id: str, kbid: str, search_references
):
    """Process swagger search actions."""
    try:
        search_results = await _perform_swagger_search(query=query, index_name=kbid)

        for result in search_results:
            search_references.add_search_result(
                type="file",
                name=result.get("name", ""),
                path=result.get("name", ""),
                content=json.dumps(result.get("additional_metadata", {})),
            )

        final_content = json.dumps(search_results)

        return [
            {"role": "tool", "name": tool_id, "content": final_content}
        ], search_references

    except Exception as e:
        print(f"Error in process_swagger_search: {e}")
        raise
